# DICOM 图像查看器组件

这是一个基于 Vue 3 和 Cornerstone.js 的 DICOM 图像查看器组件，支持图像浏览、窗宽窗位调节、滚轮缩放等功能。

## 功能特性

- 📖 DICOM 图像序列浏览
- 🖱️ 鼠标滚轮切换图像
- 🎛️ 窗宽窗位调节 (WW/WL)
- 📊 自定义滚动条
- 🔄 图像预加载和缓存
- 📱 响应式设计
- 🎯 事件回调支持

## 安装依赖

```bash
npm install cornerstone-core cornerstone-tools cornerstone-wado-image-loader cornerstone-math hammerjs dicom-parser
```

## 基本使用

```vue
<template>
  <DicomViewer
    :series-path="'wadouri:http://localhost:5173/dcm1/'"
    :number-of-images="427"
    :image-prefix="'56364'"
    :start-index="397"
    :container-width="1000"
    :container-height="800"
    @image-changed="handleImageChanged"
    @viewport-changed="handleViewportChanged"
  />
</template>

<script setup>
import DicomViewer from "./components/DicomViewer.vue";

function handleImageChanged(data) {
  console.log("图像已切换:", data);
}

function handleViewportChanged(data) {
  console.log("视口已更新:", data);
}
</script>
```

## Props 属性

| 属性名            | 类型   | 默认值                                  | 描述                 |
| ----------------- | ------ | --------------------------------------- | -------------------- |
| `seriesPath`      | String | `"wadouri:http://localhost:5173/dcm1/"` | DICOM 文件的基础路径 |
| `numberOfImages`  | Number | `427`                                   | 图像序列中的图像总数 |
| `imagePrefix`     | String | `"56364"`                               | 图像文件名前缀       |
| `startIndex`      | Number | `397`                                   | 图像文件名的起始索引 |
| `containerWidth`  | Number | `1000`                                  | 容器宽度（像素）     |
| `containerHeight` | Number | `800`                                   | 容器高度（像素）     |
| `cacheRange`      | Number | `2`                                     | 预加载图像的范围     |
| `visibleImages`   | Number | `1`                                     | 可见图像数量         |

## 事件

### `@image-changed`

当切换到新图像时触发

**参数:**

```javascript
{
  index: number,        // 当前图像索引
  imageId: string,      // 图像ID
  image: Object         // Cornerstone 图像对象
}
```

### `@viewport-changed`

当视口参数改变时触发

**参数:**

```javascript
{
  windowWidth: number,  // 窗宽
  windowLevel: number,  // 窗位
  imageNumber: number,  // 当前图像编号
  totalImages: number   // 总图像数
}
```

## 暴露的方法

通过 `ref` 可以访问组件的以下方法：

```vue
<template>
  <DicomViewer ref="viewerRef" ... />
</template>

<script setup>
import { ref } from "vue";

const viewerRef = ref();

// 加载指定索引的图像
viewerRef.value.loadAndDisplayImage(10);

// 获取当前图像索引
const currentIndex = viewerRef.value.getCurrentImageIndex();

// 获取视口信息
const viewportInfo = viewerRef.value.getViewportInfo();
</script>
```

## 多个查看器示例

参考 `src/examples/MultipleViewers.vue` 文件，了解如何在同一页面中使用多个查看器。

## 操作说明

- **鼠标滚轮**: 上下滚动切换图像
- **鼠标左键拖拽**: 调节窗宽窗位
- **滚动条拖拽**: 快速跳转到指定图像

## 注意事项

1. 确保 DICOM 文件路径正确且可访问
2. 图像文件名格式需要与 `imagePrefix` 和 `startIndex` 配置匹配
3. 建议在生产环境中配置适当的 CORS 策略
4. 大量图像时建议调整 `cacheRange` 以优化性能

## 自定义样式

组件使用 scoped 样式，可以通过 CSS 变量或深度选择器进行自定义：

```css
/* 自定义滚动条样式 */
:deep(.scrollbar) {
  background: rgba(100, 100, 100, 0.3);
}

/* 自定义信息面板样式 */
:deep(.info-panel) {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
}
```

## 开发和构建

```bash
# 安装依赖
npm install

# 开发模式
npm run dev

# 构建
npm run build
```
