<script setup>
import { onMounted, onBeforeUnmount, computed, ref } from "vue";
import * as cornerstone from "cornerstone-core";
import * as cornerstoneTools from "cornerstone-tools";
import * as Hammer from "hammerjs";
import * as cornerstoneMath from "cornerstone-math";
import * as dicomParser from "dicom-parser";
import * as cornerstoneWADOImageLoader from "cornerstone-wado-image-loader";

// Props 定义
const props = defineProps({
  seriesPath: {
    type: String,
    required: true,
    default: "wadouri:http://localhost:5173/dcm1/"
  },
  numberOfImages: {
    type: Number,
    required: true,
    default: 427
  },
  imagePrefix: {
    type: String,
    default: "56364"
  },
  startIndex: {
    type: Number,
    default: 397
  },
  containerWidth: {
    type: Number,
    default: 1000
  },
  containerHeight: {
    type: Number,
    default: 800
  },
  cacheRange: {
    type: Number,
    default: 2
  },
  visibleImages: {
    type: Number,
    default: 1
  }
});

// Emits 定义
const emit = defineEmits(['imageChanged', 'viewportChanged']);

// 响应式变量
const ww = ref();
const wl = ref();
const se = ref(1);
const im = ref(1);
const thumbPosition = ref(0);
const element = ref();
const isDragging = ref(false);

// 动态生成 imageId 列表
const stack = {
  currentImageIdIndex: 0,
  imageIds: Array.from(
    { length: props.numberOfImages },
    (_, i) => `${props.seriesPath}${props.imagePrefix}${String(i + props.startIndex).padStart(3, "0")}.dcm`
  ),
};

// 动态计算滑块的高度
const thumbHeight = computed(() =>
  Math.max((props.visibleImages / props.numberOfImages) * props.containerHeight, 20)
);

// 加载和显示图像
function loadAndDisplayImage(index) {
  if (index < 0 || index >= stack.imageIds.length) return;

  cornerstone.loadAndCacheImage(stack.imageIds[index]).then((image) => {
    console.log(image, stack.imageIds[index], "+++++++++");

    stack.currentImageIdIndex = index;
    cornerstone.displayImage(element.value, image);
    const viewport = cornerstone.getViewport(element.value);
    ww.value = Math.round(viewport.voi.windowWidth);
    wl.value = Math.round(viewport.voi.windowCenter);
    im.value = stack.currentImageIdIndex + 1;
    
    // 发射事件
    emit('imageChanged', {
      index: stack.currentImageIdIndex,
      imageId: stack.imageIds[index],
      image: image
    });
    
    emit('viewportChanged', {
      windowWidth: ww.value,
      windowLevel: wl.value,
      imageNumber: im.value,
      totalImages: props.numberOfImages
    });
    
    preloadImages(index);
    updateThumbPosition();
  });
}

// 预加载附近的图像
function preloadImages(currentIndex) {
  const start = Math.max(currentIndex - props.cacheRange, 0);
  const end = Math.min(currentIndex + props.cacheRange, stack.imageIds.length - 1);

  for (let i = start; i <= end; i++) {
    if (i !== currentIndex) {
      cornerstone.loadAndCacheImage(stack.imageIds[i]);
    }
  }
}

// 更新滚动条拇指的位置
function updateThumbPosition() {
  const scrollableHeight = props.containerHeight - thumbHeight.value;
  thumbPosition.value =
    (scrollableHeight * stack.currentImageIdIndex) / (props.numberOfImages - 1);
}

// 处理滚动条拖动
function startDragging() {
  isDragging.value = true;
  document.addEventListener("mousemove", drag);
  document.addEventListener("mouseup", stopDragging);
}

function drag(event) {
  if (!isDragging.value) return;

  const scrollableHeight = props.containerHeight - thumbHeight.value;
  const rect = document.querySelector(".scrollbar").getBoundingClientRect();
  const thumbOffset = thumbHeight.value / 2;
  const newPosition = Math.min(
    Math.max(event.clientY - rect.top - thumbOffset, 0),
    scrollableHeight
  );

  thumbPosition.value = newPosition;
  const newIndex = Math.round(
    (newPosition / scrollableHeight) * (props.numberOfImages - 1)
  );
  loadAndDisplayImage(newIndex);
}

function stopDragging() {
  isDragging.value = false;
  document.removeEventListener("mousemove", drag);
  document.removeEventListener("mouseup", stopDragging);
}

// 暴露给父组件的方法
defineExpose({
  loadAndDisplayImage,
  getCurrentImageIndex: () => stack.currentImageIdIndex,
  getViewportInfo: () => ({
    windowWidth: ww.value,
    windowLevel: wl.value,
    imageNumber: im.value,
    totalImages: props.numberOfImages
  })
});

// 初始化 cornerstone 和工具
onMounted(() => {
  cornerstoneTools.external.cornerstone = cornerstone;
  cornerstoneTools.external.cornerstoneMath = cornerstoneMath;
  cornerstoneTools.external.Hammer = Hammer;
  cornerstoneWADOImageLoader.external.dicomParser = dicomParser;
  cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
  cornerstoneTools.init();

  element.value = document.querySelector("#cornerstone");
  cornerstone.enable(element.value);

  cornerstoneTools.addTool(cornerstoneTools.WwwcTool);
  cornerstoneTools.setToolActive("Wwwc", { mouseButtonMask: 1 });

  // 鼠标滚轮事件监听
  const handleScroll = (event) => {
    event.preventDefault();
    const delta = Math.sign(event.deltaY);
    const newIndex = stack.currentImageIdIndex - delta;
    if (newIndex >= 0 && newIndex < stack.imageIds.length) {
      loadAndDisplayImage(newIndex);
    }
  };

  element.value.addEventListener("wheel", handleScroll);

  // 加载初始图像
  loadAndDisplayImage(stack.currentImageIdIndex);

  // 清理事件监听器
  onBeforeUnmount(() => {
    element.value.removeEventListener("wheel", handleScroll);
  });
});
</script>

<template>
  <div 
    class="scroll-container" 
    :style="{ width: containerWidth + 'px', height: containerHeight + 'px' }"
  >
    <div id="cornerstone" class="cornerstone"></div>
    <div class="scrollbar">
      <div
        class="thumb"
        :style="{ top: thumbPosition + 'px', height: thumbHeight + 'px' }"
        @mousedown="startDragging"
      ></div>
    </div>
    <div class="info-panel">
      <div>WW: {{ ww }} , WL: {{ wl }}</div>
      <div>SE: {{ se }}</div>
      <div>IM: {{ im }} / {{ numberOfImages }}</div>
    </div>
  </div>
</template>

<style scoped>
.scroll-container {
  position: relative;
}
.cornerstone {
  width: 100%;
  height: 100%;
}
.scrollbar {
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  height: 100%;
  background: rgba(200, 200, 200, 0.5);
}
.thumb {
  position: absolute;
  width: 100%;
  background: #ffffff;
  cursor: pointer;
}
.info-panel {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 14px;
}
</style>
