<script setup>
import { onMounted, onBeforeUnmount, computed, ref } from "vue";
import * as cornerstone from "cornerstone-core";
import * as cornerstoneTools from "cornerstone-tools";
import * as Hammer from "hammerjs";
import * as cornerstoneMath from "cornerstone-math";
import * as dicomParser from "dicom-parser";
import * as cornerstoneWADOImageLoader from "cornerstone-wado-image-loader";

// 常量与ref变量
const cacheRange = 2;
const ww = ref();
const wl = ref();
const se = ref(1); // 假设每个序列只有一组图像
const im = ref(1);
const thumbPosition = ref(0);
const containerHeight = 800;
const visibleImages = 1;
let numberOfImages = 427;
let seriesPath = "wadouri:http://localhost:5173/dcm1/";
const element = ref();
const isDragging = ref(false);

// 动态生成 imageId 列表
const stack = {
  currentImageIdIndex: 0,
  imageIds: Array.from(
    { length: numberOfImages },
    (_, i) => `${seriesPath}56364${String(i + 397).padStart(3, "0")}.dcm`
  ),
};

// 动态计算滑块的高度
const thumbHeight = computed(() =>
  Math.max((visibleImages / numberOfImages) * containerHeight, 20)
);

// 加载和显示图像
function loadAndDisplayImage(index) {
  if (index < 0 || index >= stack.imageIds.length) return;

  cornerstone.loadAndCacheImage(stack.imageIds[index]).then((image) => {
    console.log(image, stack.imageIds[index], "+++++++++");

    stack.currentImageIdIndex = index;
    cornerstone.displayImage(element.value, image);
    const viewport = cornerstone.getViewport(element.value);
    ww.value = Math.round(viewport.voi.windowWidth);
    wl.value = Math.round(viewport.voi.windowCenter);
    im.value = stack.currentImageIdIndex + 1;
    preloadImages(index);
    updateThumbPosition();
  });
}

// 预加载附近的图像
function preloadImages(currentIndex) {
  const start = Math.max(currentIndex - cacheRange, 0);
  const end = Math.min(currentIndex + cacheRange, stack.imageIds.length - 1);

  for (let i = start; i <= end; i++) {
    if (i !== currentIndex) {
      cornerstone.loadAndCacheImage(stack.imageIds[i]);
    }
  }
}

// 更新滚动条拇指的位置
function updateThumbPosition() {
  const scrollableHeight = containerHeight - thumbHeight.value;
  thumbPosition.value =
    (scrollableHeight * stack.currentImageIdIndex) / (numberOfImages - 1);
}

// 处理滚动条拖动
function startDragging() {
  isDragging.value = true;
  document.addEventListener("mousemove", drag);
  document.addEventListener("mouseup", stopDragging);
}

function drag(event) {
  if (!isDragging.value) return;

  const scrollableHeight = containerHeight - thumbHeight.value;
  const rect = document.querySelector(".scrollbar").getBoundingClientRect();
  const thumbOffset = thumbHeight.value / 2;
  const newPosition = Math.min(
    Math.max(event.clientY - rect.top - thumbOffset, 0),
    scrollableHeight
  );

  thumbPosition.value = newPosition;
  const newIndex = Math.round(
    (newPosition / scrollableHeight) * (numberOfImages - 1)
  );
  loadAndDisplayImage(newIndex);
}

function stopDragging() {
  isDragging.value = false;
  document.removeEventListener("mousemove", drag);
  document.removeEventListener("mouseup", stopDragging);
}

// 初始化 cornerstone 和工具
onMounted(() => {
  cornerstoneTools.external.cornerstone = cornerstone;
  cornerstoneTools.external.cornerstoneMath = cornerstoneMath;
  cornerstoneTools.external.Hammer = Hammer;
  cornerstoneWADOImageLoader.external.dicomParser = dicomParser;
  cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
  cornerstoneTools.init();

  element.value = document.querySelector("#cornerstone");
  cornerstone.enable(element.value);

  cornerstoneTools.addTool(cornerstoneTools.WwwcTool);
  cornerstoneTools.setToolActive("Wwwc", { mouseButtonMask: 1 });

  // 鼠标滚轮事件监听
  const handleScroll = (event) => {
    event.preventDefault();
    const delta = Math.sign(event.deltaY);
    const newIndex = stack.currentImageIdIndex - delta;
    if (newIndex >= 0 && newIndex < stack.imageIds.length) {
      loadAndDisplayImage(newIndex);
    }
  };

  element.value.addEventListener("wheel", handleScroll);

  // 加载初始图像
  loadAndDisplayImage(stack.currentImageIdIndex);

  // 清理事件监听器
  onBeforeUnmount(() => {
    element.value.removeEventListener("wheel", handleScroll);
  });
});
</script>

<template>
  <div class="scroll-container">
    <div id="cornerstone" class="cornerstone"></div>
    <div class="scrollbar">
      <div
        class="thumb"
        :style="{ top: thumbPosition + 'px', height: thumbHeight + 'px' }"
        @mousedown="startDragging"
      ></div>
    </div>
    <div>WW: {{ ww }} , WL: {{ wl }}</div>
    <div>SE: {{ se }}</div>
    <div>IM: {{ im }} / {{ numberOfImages }}</div>
  </div>
</template>

<style scoped>
.scroll-container {
  position: relative;
  width: 1000px;
  height: 800px;
}
.cornerstone {
  width: 100%;
  height: 100%;
}
.scrollbar {
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  height: 100%;
  background: rgba(200, 200, 200, 0.5);
}
.thumb {
  position: absolute;
  width: 100%;
  background: #ffffff;
  cursor: pointer;
}
</style>
