<script setup>
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./components/DicomViewer.vue";

// 组件配置
const viewerConfig = {
  seriesPath: "wadouri:http://localhost:5173/dcm1/",
  numberOfImages: 427,
  imagePrefix: "56364",
  startIndex: 397,
  containerWidth: 1000,
  containerHeight: 800,
  cacheRange: 2,
  visibleImages: 1,
};

// 事件处理函数
function handleImageChanged(data) {
  console.log("图像已切换:", data);
}

function handleViewportChanged(data) {
  console.log("视口已更新:", data);
}
</script>

<template>
  <div class="app-container">
    <h1>DICOM 图像查看器</h1>
    <DicomViewer
      :series-path="viewerConfig.seriesPath"
      :number-of-images="viewerConfig.numberOfImages"
      :image-prefix="viewerConfig.imagePrefix"
      :start-index="viewerConfig.startIndex"
      :container-width="viewerConfig.containerWidth"
      :container-height="viewerConfig.containerHeight"
      :cache-range="viewerConfig.cacheRange"
      :visible-images="viewerConfig.visibleImages"
      @image-changed="handleImageChanged"
      @viewport-changed="handleViewportChanged"
    />
  </div>
</template>

<style scoped>
.app-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

h1 {
  margin-bottom: 20px;
  color: #333;
}
</style>
