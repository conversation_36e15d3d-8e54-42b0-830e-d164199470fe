<script setup>
import { ref } from "vue";
import DicomViewer from "../components/DicomViewer.vue";

// 多个查看器的配置
const viewer1Config = {
  seriesPath: "wadouri:http://localhost:5173/dcm1/",
  numberOfImages: 427,
  imagePrefix: "56364",
  startIndex: 397,
  containerWidth: 500,
  containerHeight: 400,
  cacheRange: 2,
  visibleImages: 1
};

const viewer2Config = {
  seriesPath: "wadouri:http://localhost:5173/dcm2/",
  numberOfImages: 300,
  imagePrefix: "IMG",
  startIndex: 1,
  containerWidth: 500,
  containerHeight: 400,
  cacheRange: 3,
  visibleImages: 1
};

// 当前选中的查看器
const activeViewer = ref(1);

// 事件处理函数
function handleImageChanged(viewerId, data) {
  console.log(`查看器 ${viewerId} 图像已切换:`, data);
}

function handleViewportChanged(viewerId, data) {
  console.log(`查看器 ${viewerId} 视口已更新:`, data);
}
</script>

<template>
  <div class="multiple-viewers-container">
    <h1>多个 DICOM 查看器示例</h1>
    
    <div class="viewer-controls">
      <button 
        :class="{ active: activeViewer === 1 }" 
        @click="activeViewer = 1"
      >
        查看器 1
      </button>
      <button 
        :class="{ active: activeViewer === 2 }" 
        @click="activeViewer = 2"
      >
        查看器 2
      </button>
    </div>

    <div class="viewers-grid">
      <div class="viewer-wrapper">
        <h3>系列 1</h3>
        <DicomViewer
          :series-path="viewer1Config.seriesPath"
          :number-of-images="viewer1Config.numberOfImages"
          :image-prefix="viewer1Config.imagePrefix"
          :start-index="viewer1Config.startIndex"
          :container-width="viewer1Config.containerWidth"
          :container-height="viewer1Config.containerHeight"
          :cache-range="viewer1Config.cacheRange"
          :visible-images="viewer1Config.visibleImages"
          @image-changed="(data) => handleImageChanged(1, data)"
          @viewport-changed="(data) => handleViewportChanged(1, data)"
        />
      </div>

      <div class="viewer-wrapper">
        <h3>系列 2</h3>
        <DicomViewer
          :series-path="viewer2Config.seriesPath"
          :number-of-images="viewer2Config.numberOfImages"
          :image-prefix="viewer2Config.imagePrefix"
          :start-index="viewer2Config.startIndex"
          :container-width="viewer2Config.containerWidth"
          :container-height="viewer2Config.containerHeight"
          :cache-range="viewer2Config.cacheRange"
          :visible-images="viewer2Config.visibleImages"
          @image-changed="(data) => handleImageChanged(2, data)"
          @viewport-changed="(data) => handleViewportChanged(2, data)"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.multiple-viewers-container {
  padding: 20px;
}

.viewer-controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.viewer-controls button {
  padding: 10px 20px;
  border: 1px solid #ccc;
  background: white;
  cursor: pointer;
  border-radius: 4px;
}

.viewer-controls button.active {
  background: #007bff;
  color: white;
}

.viewers-grid {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.viewer-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.viewer-wrapper h3 {
  margin-bottom: 10px;
  color: #333;
}
</style>
