{"name": "dcmz", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview"}, "dependencies": {"cornerstone-core": "^2.6.1", "cornerstone-math": "^0.1.10", "cornerstone-tools": "^6.0.10", "cornerstone-wado-image-loader": "^4.13.2", "hammerjs": "^2.0.8", "vue": "^3.5.10"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "vite": "^5.4.8"}}